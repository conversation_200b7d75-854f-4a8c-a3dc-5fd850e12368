import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Code2, 
  Database, 
  Settings, 
  Zap,
  User,
  HelpCircle,
  Clock,
  ArrowRight,
  Sparkles
} from "lucide-react";

const ExpertComparisonSection = () => {
  return (
    <section className="px-4 sm:px-6 lg:px-8 py-24 bg-muted/30">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-20">
          <h2 className="text-5xl lg:text-6xl font-bold text-foreground mb-8">
            Why AI Is Still Too Hard
          </h2>
          <p className="text-2xl text-muted-foreground max-w-4xl mx-auto leading-relaxed">
            Most AI tools require technical expertise. DataGent changes that by making 
            expert-level AI accessible to everyone.
          </p>
        </div>

        {/* Comparison Grid */}
        <div className="grid lg:grid-cols-3 gap-8 mb-16">
          {/* AI Experts Column */}
          <Card className="border-0 shadow-xl hover:shadow-2xl transition-all duration-500 bg-card">
            <CardContent className="p-8">
              <div className="text-center mb-8">
                <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Code2 className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-foreground mb-2">AI Experts</h3>
                <Badge variant="secondary" className="text-sm text-secondary-foreground">Technical Skills Required</Badge>
              </div>
              
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <Database className="w-5 h-5 text-blue-500 mt-1 flex-shrink-0" />
                  <div>
                    <p className="font-semibold text-foreground">Custom Data Pipelines</p>
                    <p className="text-sm text-muted-foreground">Build complex workflows from scratch</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <Settings className="w-5 h-5 text-blue-500 mt-1 flex-shrink-0" />
                  <div>
                    <p className="font-semibold text-foreground">Advanced Prompt Engineering</p>
                    <p className="text-sm text-muted-foreground">Craft perfect prompts through trial and error</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <Code2 className="w-5 h-5 text-blue-500 mt-1 flex-shrink-0" />
                  <div>
                    <p className="font-semibold text-foreground">API Integration</p>
                    <p className="text-sm text-muted-foreground">Connect multiple AI services manually</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <Zap className="w-5 h-5 text-blue-500 mt-1 flex-shrink-0" />
                  <div>
                    <p className="font-semibold text-foreground">Model Fine-tuning</p>
                    <p className="text-sm text-muted-foreground">Optimize AI models for specific tasks</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Regular Users Column */}
          <Card className="border-0 shadow-xl hover:shadow-2xl transition-all duration-500 bg-card">
            <CardContent className="p-8">
              <div className="text-center mb-8">
                <div className="w-16 h-16 bg-orange-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <User className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-foreground mb-2">Regular Users</h3>
                <Badge variant="outline" className="text-sm border-muted-foreground text-foreground">Stuck with Basic Tools</Badge>
              </div>
              
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <HelpCircle className="w-5 h-5 text-orange-500 mt-1 flex-shrink-0" />
                  <div>
                    <p className="font-semibold text-foreground">Confusing Interfaces</p>
                    <p className="text-sm text-muted-foreground">Complex tools with steep learning curves</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <Clock className="w-5 h-5 text-orange-500 mt-1 flex-shrink-0" />
                  <div>
                    <p className="font-semibold text-foreground">Manual Processes</p>
                    <p className="text-sm text-muted-foreground">Hours of repetitive work</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <HelpCircle className="w-5 h-5 text-orange-500 mt-1 flex-shrink-0" />
                  <div>
                    <p className="font-semibold text-foreground">Generic Outputs</p>
                    <p className="text-sm text-muted-foreground">One-size-fits-all solutions</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <Clock className="w-5 h-5 text-orange-500 mt-1 flex-shrink-0" />
                  <div>
                    <p className="font-semibold text-foreground">Limited Results</p>
                    <p className="text-sm text-muted-foreground">Basic insights, minimal impact</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* DataGent Solution Column */}
          <Card className="border-0 shadow-xl hover:shadow-2xl transition-all duration-500 bg-gradient-to-br from-purple-50 to-blue-50 border border-primary/20">
            <CardContent className="p-8">
              <div className="text-center mb-8">
                <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center mx-auto mb-4 p-2">
                  <img src="/lovable-uploads/0d82894e-1097-4453-aa86-f8d3badb9013.png" alt="DataGent Logo" className="w-full h-full object-contain" />
                </div>
                <h3 className="text-2xl font-bold text-black mb-2">DataGent</h3>
                <Badge className="bg-gradient-to-r from-purple-500 to-blue-500 text-white text-sm">Expert Power, Simple Interface</Badge>
              </div>
              
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <Sparkles className="w-5 h-5 text-purple-500 mt-1 flex-shrink-0" />
                  <div>
                    <p className="font-semibold text-black">Pre-built AI Agents</p>
                    <p className="text-sm text-gray-600">Ready-to-use specialists for every need</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <Zap className="w-5 h-5 text-purple-500 mt-1 flex-shrink-0" />
                  <div>
                    <p className="font-semibold text-black">One-Click Setup</p>
                    <p className="text-sm text-gray-600">Get started in minutes, not months</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <Settings className="w-5 h-5 text-purple-500 mt-1 flex-shrink-0" />
                  <div>
                    <p className="font-semibold text-black">Smart Automation</p>
                    <p className="text-sm text-gray-600">Expert-level workflows, zero coding</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <Database className="w-5 h-5 text-purple-500 mt-1 flex-shrink-0" />
                  <div>
                    <p className="font-semibold text-black">Tailored Insights</p>
                    <p className="text-sm text-gray-600">Personalized for your business goals</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Visual Flow */}
        <div className="flex items-center justify-center space-x-8 mb-16">
          <div className="text-center">
            <div className="w-20 h-20 bg-orange-500/20 border border-orange-500/30 rounded-full flex items-center justify-center mb-4">
              <User className="w-10 h-10 text-orange-500" />
            </div>
            <p className="text-lg font-semibold text-foreground">Frustrated User</p>
            <p className="text-sm text-muted-foreground">Complex AI tools</p>
          </div>
          
          <ArrowRight className="w-8 h-8 text-muted-foreground" />
          
          <div className="text-center">
            <div className="w-20 h-20 bg-primary/20 border border-primary/30 rounded-full flex items-center justify-center mb-4">
              <Sparkles className="w-10 h-10 text-primary" />
            </div>
            <p className="text-lg font-semibold text-foreground">DataGent</p>
            <p className="text-sm text-muted-foreground">Bridges the gap</p>
          </div>
          
          <ArrowRight className="w-8 h-8 text-muted-foreground" />
          
          <div className="text-center">
            <div className="w-20 h-20 bg-blue-500/20 border border-blue-500/30 rounded-full flex items-center justify-center mb-4">
              <Code2 className="w-10 h-10 text-blue-500" />
            </div>
            <p className="text-lg font-semibold text-foreground">Expert Results</p>
            <p className="text-sm text-muted-foreground">Without the complexity</p>
          </div>
        </div>

        {/* Bottom CTA */}
        <div className="text-center">
          <div className="bg-muted/30 border border-primary/20 rounded-3xl p-12">
            <h3 className="text-3xl font-bold text-foreground mb-4">
              Stop Fighting with AI Tools
            </h3>
            <p className="text-xl text-muted-foreground mb-6 max-w-2xl mx-auto">
              Get the power of AI experts without the technical headaches. 
              DataGent makes advanced AI simple enough for anyone to use.
            </p>
            <Badge className="bg-gradient-to-r from-purple-500 to-blue-500 text-white text-lg px-6 py-2">
              Expert AI Made Simple
            </Badge>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ExpertComparisonSection;