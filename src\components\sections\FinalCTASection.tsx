
import React, { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON><PERSON><PERSON>, ArrowR<PERSON>, Rocket, Star, Zap } from "lucide-react";
import { WaitlistForm } from "@/components/WaitlistForm";
import type { FinalCTASectionProps } from '@/types';

const FinalCTASection: React.FC<FinalCTASectionProps> = ({ className }) => {
  const [isWaitlistOpen, setIsWaitlistOpen] = useState(false);

  return (
    <section className="px-4 sm:px-6 lg:px-8 py-24 gradient-primary relative overflow-hidden">
      {/* Background effects */}
      <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmYiIGZpbGwtb3BhY2l0eT0iMC4xIj48cGF0aCBkPSJtMzYgMzQgNCAwIDAgNCAtNCAweiIvPjwvZz48L2c+PC9zdmc+')] opacity-20"></div>
      
      <div className="max-w-5xl mx-auto text-center text-white relative z-10">
        <div className="mb-8">
          <Star className="w-12 h-12 mx-auto mb-4 text-yellow-300" />
          <h2 className="text-5xl lg:text-6xl font-bold mb-8 leading-tight">
            Ready to Transform Your Business?
          </h2>
          <p className="text-2xl mb-6 opacity-90 leading-relaxed">
            Join the smart business owners who are already winning with AI
          </p>
          <p className="text-xl opacity-75">
            Get early access to DataGent and be among the first to experience AI that actually works for your business.
          </p>
        </div>
        
        {/* Urgency and social proof */}
        <div className="grid md:grid-cols-3 gap-6 mb-12">
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
            <Rocket className="w-8 h-8 mx-auto mb-3 text-yellow-300" />
            <h3 className="text-lg font-semibold mb-2">Limited Beta</h3>
            <p className="text-sm opacity-80">Only 100 spots left</p>
          </div>
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
            <Zap className="w-8 h-8 mx-auto mb-3 text-yellow-300" />
            <h3 className="text-lg font-semibold mb-2">Early Bird Pricing</h3>
            <p className="text-sm opacity-80">50% off for beta users</p>
          </div>
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
            <CheckCircle className="w-8 h-8 mx-auto mb-3 text-green-300" />
            <h3 className="text-lg font-semibold mb-2">No Risk</h3>
            <p className="text-sm opacity-80">30-day money back guarantee</p>
          </div>
        </div>
        
        <div className="flex justify-center mb-12">
          <Button 
            onClick={() => setIsWaitlistOpen(true)}
            className="bg-white text-primary hover:bg-gray-100 h-16 px-8 text-lg font-bold shadow-2xl hover:shadow-3xl transition-all duration-300 group min-w-[200px]"
          >
            <Rocket className="mr-3 w-6 h-6 group-hover:rotate-12 transition-transform duration-300" />
            Get Early Access
            <ArrowRight className="ml-3 w-6 h-6 group-hover:translate-x-1 transition-transform duration-300" />
          </Button>
        </div>
        
        <div className="flex flex-wrap items-center justify-center gap-8 text-base opacity-90">
          <div className="flex items-center space-x-3">
            <CheckCircle className="w-6 h-6 text-green-300" />
            <span className="font-medium">Free to join</span>
          </div>
          <div className="flex items-center space-x-3">
            <CheckCircle className="w-6 h-6 text-green-300" />
            <span className="font-medium">Early access</span>
          </div>
          <div className="flex items-center space-x-3">
            <CheckCircle className="w-6 h-6 text-green-300" />
            <span className="font-medium">Special pricing</span>
          </div>
          <div className="flex items-center space-x-3">
            <CheckCircle className="w-6 h-6 text-green-300" />
            <span className="font-medium">Cancel anytime</span>
          </div>
        </div>
      </div>
      
      <WaitlistForm 
        isOpen={isWaitlistOpen} 
        onClose={() => setIsWaitlistOpen(false)} 
      />
    </section>
  );
};

export default FinalCTASection;
