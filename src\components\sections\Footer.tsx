
import React from 'react';
import { <PERSON>, <PERSON>, Linkedin, MessageSquare } from "lucide-react";

const Footer = () => {
  return (
    <footer className="px-4 sm:px-6 lg:px-8 py-16 bg-foreground text-background">
      <div className="max-w-7xl mx-auto">
        <div className="grid md:grid-cols-4 gap-12">
          <div className="col-span-2">
            <div className="flex items-center space-x-3 mb-8">
              <div className="w-10 h-10 gradient-primary rounded-xl flex items-center justify-center shadow-lg">
                <Brain className="w-6 h-6 text-white" />
              </div>
              <span className="text-3xl font-bold tracking-tight">DataGent</span>
            </div>
            <p className="text-muted-foreground mb-8 max-w-md text-lg leading-relaxed">
              AI-powered productivity platform helping businesses across Africa grow with intelligent 
              data insights and automation that actually works.
            </p>
            <div className="space-y-2 text-muted-foreground">
              <p className="flex items-center text-base">
                <span className="text-xl mr-2">🏢</span> 
                Based in Nairobi, Kenya
              </p>
              <p className="flex items-center text-base">
                <span className="text-xl mr-2">🌍</span> 
                Serving businesses globally
              </p>
              <p className="flex items-center text-base">
                <span className="text-xl mr-2">🚀</span> 
                500+ businesses on waitlist
              </p>
            </div>
          </div>
          
          <div>
            <h4 className="font-bold mb-6 text-lg">Connect</h4>
            <div className="space-y-4">
              <a href="#" className="flex items-center space-x-3 text-muted-foreground hover:text-background transition-colors group">
                <Twitter className="w-5 h-5 group-hover:scale-110 transition-transform" />
                <span>Follow updates</span>
              </a>
              <a href="#" className="flex items-center space-x-3 text-muted-foreground hover:text-background transition-colors group">
                <Linkedin className="w-5 h-5 group-hover:scale-110 transition-transform" />
                <span>Connect on LinkedIn</span>
              </a>
              <a href="#" className="flex items-center space-x-3 text-muted-foreground hover:text-background transition-colors group">
                <MessageSquare className="w-5 h-5 group-hover:scale-110 transition-transform" />
                <span>Read our blog</span>
              </a>
            </div>
          </div>
          
          <div>
            <h4 className="font-bold mb-6 text-lg">Beta Program</h4>
            <div className="space-y-3 text-muted-foreground">
              <p className="flex items-center">
                <span className="text-lg mr-2">🚀</span> 
                Currently in private beta
              </p>
              <p className="flex items-center">
                <span className="text-lg mr-2">💼</span> 
                Raising pre-seed round
              </p>
              <p className="flex items-center">
                <span className="text-lg mr-2">⭐</span> 
                Join 500+ on waitlist
              </p>
            </div>
          </div>
        </div>
        
        <div className="border-t border-muted mt-16 pt-8 text-center text-muted-foreground">
          <p className="text-lg">&copy; 2024 DataGent. Built with ❤️ in Nairobi, Kenya.</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
