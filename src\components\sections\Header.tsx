
import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Brain, Menu, X } from "lucide-react";
import { ThemeToggle } from "@/components/theme-toggle";
import type { NavigationItem } from '@/types';

const navigationItems: NavigationItem[] = [
  { label: "How it works", href: "#how-it-works" },
  { label: "Success stories", href: "#success-stories" },
  { label: "Reviews", href: "#testimonials" },
];

const Header = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToSection = (href: string) => {
    const element = document.querySelector(href);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
      setIsMobileMenuOpen(false);
    }
  };

  return (
    <header 
      className={`sticky top-0 z-50 px-4 sm:px-6 lg:px-8 py-4 transition-all duration-300 ${
        isScrolled 
          ? 'bg-background/95 backdrop-blur-md border-b border-border/20 shadow-sm' 
          : 'bg-transparent'
      }`}
      role="banner"
    >
      <div className="max-w-7xl mx-auto flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 gradient-primary rounded-xl flex items-center justify-center shadow-lg">
            <Brain className="w-6 h-6 text-white" aria-hidden="true" />
          </div>
          <span className="text-2xl font-bold text-foreground tracking-tight">
            DataGent
          </span>
        </div>
        
        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center space-x-6" role="navigation">
          {navigationItems.map((item) => (
            <button
              key={item.href}
              onClick={() => scrollToSection(item.href)}
              className="text-muted-foreground hover:text-foreground transition-colors font-medium focus:outline-none focus:text-foreground relative after:absolute after:bottom-0 after:left-0 after:w-0 after:h-0.5 after:bg-primary after:transition-all after:duration-300 hover:after:w-full"
            >
              {item.label}
            </button>
          ))}
          <ThemeToggle />
          <Button 
            onClick={() => scrollToSection('#waitlist')}
            variant="cta"
            className="gradient-primary border-0 text-white hover:opacity-90 transition-all duration-300"
          >
            Join Waitlist
          </Button>
        </nav>

        {/* Mobile Menu Button */}
        <button
          className="md:hidden p-2 text-foreground hover:bg-muted rounded-lg transition-colors"
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          aria-label="Toggle mobile menu"
          aria-expanded={isMobileMenuOpen}
        >
          {isMobileMenuOpen ? (
            <X className="w-6 h-6" aria-hidden="true" />
          ) : (
            <Menu className="w-6 h-6" aria-hidden="true" />
          )}
        </button>
      </div>

      {/* Mobile Menu */}
      {isMobileMenuOpen && (
        <div className="md:hidden mt-4 py-4 bg-background/95 backdrop-blur-md border-t border-border/20 animate-fade-in">
          <nav className="flex flex-col space-y-3" role="navigation">
            {navigationItems.map((item) => (
              <button
                key={item.href}
                onClick={() => scrollToSection(item.href)}
                className="text-left px-4 py-2 text-muted-foreground hover:text-foreground hover:bg-muted/50 rounded-lg transition-colors font-medium"
              >
                {item.label}
              </button>
            ))}
            <div className="flex items-center justify-between mx-4 mt-4">
              <ThemeToggle />
              <Button 
                onClick={() => scrollToSection('#waitlist')}
                variant="cta"
                className="gradient-primary border-0 text-white hover:opacity-90 flex-1 ml-4"
              >
                Join Waitlist
              </Button>
            </div>
          </nav>
        </div>
      )}
    </header>
  );
};

export default Header;
