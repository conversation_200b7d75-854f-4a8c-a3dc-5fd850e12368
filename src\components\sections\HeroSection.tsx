
import React, { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { 
  Bar<PERSON>hart3, 
  Target, 
  TrendingUp,
  Zap, 
  Rocket,
  ArrowRight,
  CheckCircle,
  Star
} from "lucide-react";
import { WaitlistForm } from "@/components/WaitlistForm";
import type { HeroSectionProps } from '@/types';

const HeroSection: React.FC<HeroSectionProps> = ({ className }) => {
  const [isWaitlistOpen, setIsWaitlistOpen] = useState(false);

  const aiAgents = [
    {
      icon: BarChart3,
      title: "Data Analysis",
      description: "Turn spreadsheets into insights",
      delay: "0s"
    },
    {
      icon: Target,
      title: "Marketing",
      description: "Create campaigns that convert",
      delay: "0.5s"
    },
    {
      icon: TrendingUp,
      title: "Strategy",
      description: "Make smarter decisions",
      delay: "1s"
    },
    {
      icon: Zap,
      title: "Automation",
      description: "Save hours every day",
      delay: "1.5s"
    }
  ];

  return (
    <section 
      className={`relative px-4 sm:px-6 lg:px-8 py-20 lg:py-32 overflow-hidden ${className}`}
      aria-labelledby="hero-heading"
    >
      {/* Video/GIF Background */}
      <div className="absolute inset-0 -z-20" aria-hidden="true">
        <video
          className="w-full h-full object-cover"
          autoPlay
          muted
          loop
          playsInline
          poster="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iIzhhMmJlMiIgb3BhY2l0eT0iMC4xIi8+PC9zdmc+"
        >
          <source src="https://www.learningcontainer.com/wp-content/uploads/2020/05/sample-mp4-file.mp4" type="video/mp4" />
          {/* Fallback for browsers that don't support video */}
          <img 
            src="https://images.unsplash.com/photo-1451187580459-43490279c0fa?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80" 
            alt="Technology background"
            className="w-full h-full object-cover"
          />
        </video>
      </div>
      
      {/* Overlay for better text readability */}
      <div className="absolute inset-0 bg-gradient-to-br from-background/90 via-background/80 to-background/70 -z-10" aria-hidden="true" />
      
      <div className="max-w-7xl mx-auto">
        <div className="text-center max-w-6xl mx-auto animate-fade-in">
          <Badge className="mb-8 gradient-primary text-white border-0 text-sm px-4 py-2 shadow-lg">
            <Star className="w-4 h-4 mr-2" aria-hidden="true" />
            🚀 Private Beta • Trusted by 500+ businesses
          </Badge>
          
          <h1 
            id="hero-heading"
            className="text-5xl sm:text-6xl lg:text-8xl font-bold text-foreground mb-10 leading-tight tracking-tight"
          >
            AI That Actually
            <span className="text-gradient block mt-2">Works for You</span>
          </h1>
          
          <p className="text-xl sm:text-2xl lg:text-3xl text-muted-foreground mb-12 leading-relaxed max-w-5xl mx-auto font-medium">
            Stop wrestling with complex AI tools. Get intelligent agents that understand your business, 
            speak your language, and deliver results from day one.
          </p>
          
          {/* Social proof */}
          <div className="flex flex-wrap items-center justify-center gap-6 mb-12 text-muted-foreground">
            {[
              "40% revenue increase",
              "15 hours saved weekly", 
              "No technical skills needed"
            ].map((benefit) => (
              <div key={benefit} className="flex items-center space-x-2">
                <CheckCircle className="w-5 h-5 text-green-500" aria-hidden="true" />
                <span className="font-medium">{benefit}</span>
              </div>
            ))}
          </div>
          
          {/* CTA Button */}
          <div className="flex justify-center mb-12">
            <Button 
              onClick={() => setIsWaitlistOpen(true)}
              variant="cta"
              className="h-14 px-8 text-lg group transition-all duration-300 text-white gradient-cta"
            >
              <Rocket className="mr-2 w-5 h-5 group-hover:rotate-12 transition-transform duration-300" aria-hidden="true" />
              Get Early Access
              <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" aria-hidden="true" />
            </Button>
          </div>
          
          <p className="text-muted-foreground mb-16 text-lg">
            Join entrepreneurs and teams across Kenya, Nigeria, and beyond who are already winning with AI
          </p>

          {/* AI Agents Animation */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-5xl mx-auto">
            {aiAgents.map((agent, index) => (
              <div 
                key={agent.title}
                className="flex flex-col items-center space-y-4 group"
              >
                <div className="w-20 h-20 gradient-primary rounded-3xl flex items-center justify-center shadow-xl group-hover:shadow-2xl transition-all duration-300">
                  <agent.icon className="w-10 h-10 text-white" aria-hidden="true" />
                </div>
                <div className="text-center">
                  <h3 className="text-lg font-semibold text-foreground mb-1">{agent.title}</h3>
                  <p className="text-sm text-muted-foreground">{agent.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
      
      <WaitlistForm 
        isOpen={isWaitlistOpen} 
        onClose={() => setIsWaitlistOpen(false)} 
      />
    </section>
  );
};

export default HeroSection;
