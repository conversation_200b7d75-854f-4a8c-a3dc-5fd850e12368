
import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Clock, DollarSign } from "lucide-react";
import type { SectionProps } from '@/types';

const problems = [
  {
    icon: AlertTriangle,
    title: "Generic AI gives generic results",
    description: "ChatGPT doesn't know your business, your customers, or your goals. You get cookie-cutter answers that don't move the needle.",
    color: "red"
  },
  {
    icon: Clock,
    title: "You're drowning in tools and complexity",
    description: "Analytics dashboards you can't read. Marketing tools you don't understand. Data sitting unused in spreadsheets.",
    color: "orange"
  },
  {
    icon: DollarSign,
    title: "Meanwhile, competitors are pulling ahead",
    description: "While you're stuck with manual processes, smart businesses are using AI to grow faster, serve customers better, and dominate their markets.",
    color: "red"
  }
];

const ProblemSection: React.FC<SectionProps> = ({ className }) => {
  return (
    <section 
      className={`px-4 sm:px-6 lg:px-8 py-24 bg-muted/30 relative ${className}`}
      aria-labelledby="problem-heading"
    >
      <div className="max-w-7xl mx-auto">
        {/* Header Section */}
        <div className="text-center mb-20 animate-fade-in">
          <h2 
            id="problem-heading"
            className="text-4xl sm:text-5xl font-bold text-foreground mb-6 leading-tight"
          >
            Why Most Businesses 
            <span className="text-gradient block mt-2">Fail with AI</span>
          </h2>
        </div>

        {/* Balanced Grid Layout */}
        <div className="grid lg:grid-cols-2 gap-12 lg:gap-20 items-start">
          {/* Left Column - First 2 problems */}
          <div className="space-y-8 animate-fade-in">
            {problems.slice(0, 2).map((problem, index) => (
              <div 
                key={problem.title}
                className="flex items-start space-x-6 group"
                style={{ animationDelay: `${index * 0.2}s` }}
              >
                <div className={`w-12 h-12 rounded-2xl bg-${problem.color}-100 flex items-center justify-center mt-1 group-hover:bg-${problem.color}-200 transition-colors shrink-0`}>
                  <problem.icon className={`w-6 h-6 text-${problem.color}-600`} aria-hidden="true" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-foreground mb-2">{problem.title}</h3>
                  <p className="text-muted-foreground text-lg leading-relaxed">
                    {problem.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
          
          {/* Right Column - Illustration */}
          <div className="relative animate-fade-in lg:row-span-2" style={{ animationDelay: '0.3s' }}>
            <div className="aspect-square bg-gradient-to-br from-red-100 via-orange-100 to-yellow-100 rounded-3xl relative overflow-hidden shadow-2xl hover-lift">
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent" aria-hidden="true" />
              <div className="absolute inset-8 bg-white/20 rounded-2xl backdrop-blur-sm flex items-center justify-center">
                <div className="text-center">
                  <div className="text-8xl mb-4" role="img" aria-label="Frustrated emoji">😤</div>
                  <h3 className="text-2xl font-bold text-gray-700 mb-2">Frustrated Business Owner</h3>
                  <p className="text-gray-600">Struggling with complex tools</p>
                </div>
              </div>
              
              {/* Floating elements representing complexity */}
              {['Tool #1', 'Tool #3', 'Tool #5'].map((tool, index) => (
                <div 
                  key={tool}
                  className={`absolute w-16 h-16 bg-white/30 rounded-lg flex items-center justify-center backdrop-blur-sm animate-float ${
                    index === 0 ? 'top-4 right-4' :
                    index === 1 ? 'top-1/2 left-4' : 
                    'bottom-4 left-4'
                  }`}
                  style={{ animationDelay: `${index * 0.5}s` }}
                >
                  <span className="text-sm font-medium text-gray-700">{tool}</span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Bottom Row - Third problem and Call to Action */}
        <div className="grid lg:grid-cols-2 gap-12 lg:gap-20 mt-12 lg:mt-20">
          {/* Left Column - Third problem */}
          <div className="animate-fade-in" style={{ animationDelay: '0.4s' }}>
            <div className="flex items-start space-x-6 group">
              <div className={`w-12 h-12 rounded-2xl bg-${problems[2].color}-100 flex items-center justify-center mt-1 group-hover:bg-${problems[2].color}-200 transition-colors shrink-0`}>
                {React.createElement(problems[2].icon, { className: `w-6 h-6 text-${problems[2].color}-600`, 'aria-hidden': true })}
              </div>
              <div>
                <h3 className="text-xl font-semibold text-foreground mb-2">{problems[2].title}</h3>
                <p className="text-muted-foreground text-lg leading-relaxed">
                  {problems[2].description}
                </p>
              </div>
            </div>
          </div>

          {/* Right Column - Call to Action */}
          <div className="animate-fade-in" style={{ animationDelay: '0.5s' }}>
            <div className="p-8 bg-destructive/10 dark:bg-destructive/20 rounded-3xl border border-destructive/20 hover-lift">
              <h4 className="text-2xl font-bold text-foreground mb-4">The real cost of inaction:</h4>
              <p className="text-lg text-muted-foreground leading-relaxed">
                Every day you wait is another day your competitors get smarter, faster, and more profitable. 
                <span className="font-semibold text-foreground"> Don't let AI leave you behind.</span>
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ProblemSection;
