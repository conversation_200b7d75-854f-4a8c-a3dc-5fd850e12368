
import React from 'react';
import { Badge } from "@/components/ui/badge";
import { TrendingU<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Star } from "lucide-react";

const SuccessStorySection = () => {
  return (
    <section id="success-stories" className="px-4 sm:px-6 lg:px-8 py-24 bg-gradient-to-br from-green-50/50 via-blue-50/30 to-purple-50/50">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-20">
          <Badge className="mb-8 bg-green-100 text-green-800 border-green-200 text-base px-4 py-2">
            ⭐ Success Story
          </Badge>
          
          <h2 className="text-5xl font-bold text-foreground mb-6 leading-tight">
            Meet Jane: From overwhelmed baker to 
            <span className="text-gradient block mt-2">AI-powered success</span>
          </h2>
        </div>

        {/* First Row - Story content and metrics */}
        <div className="grid lg:grid-cols-2 gap-16 items-start mb-16">
          {/* Left Column - Before/After Story */}
          <div className="space-y-8 animate-fade-in">
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 border border-border/10 shadow-lg">
              <h3 className="text-xl font-bold text-red-600 mb-4">Before DataGent:</h3>
              <p className="text-lg text-muted-foreground leading-relaxed">
                Jane spent every weekend drowning in spreadsheets, trying to figure out which pastries sold best, 
                when to run promotions, and how to reach new customers. She was juggling 5 different apps and still getting nowhere.
              </p>
            </div>
            
            <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl p-8 border border-green-200 shadow-lg">
              <h3 className="text-xl font-bold text-green-600 mb-4">After DataGent:</h3>
              <p className="text-lg text-muted-foreground leading-relaxed">
                In just 90 days, Jane increased her revenue by 40% and got her weekends back. 
                Our AI agents analyzed her sales patterns, created targeted social campaigns, and automated her inventory management.
              </p>
            </div>
          </div>
          
          {/* Right Column - Key Metrics Cards */}
          <div className="grid grid-cols-1 gap-6 animate-fade-in" style={{ animationDelay: '0.2s' }}>
            <div className="bg-card rounded-3xl p-8 shadow-xl border border-border/10 hover:shadow-2xl transition-all duration-300">
              <div className="flex items-center justify-between mb-6">
                <h4 className="text-xl font-bold text-foreground">Revenue Growth</h4>
                <TrendingUp className="w-8 h-8 text-green-500" />
              </div>
              <div className="text-5xl font-bold text-green-600 mb-2">+40%</div>
              <div className="text-muted-foreground text-lg">In just 3 months</div>
              <div className="mt-4 bg-green-100 rounded-lg p-3">
                <p className="text-sm text-green-700 font-medium">From KES 150K to KES 210K monthly</p>
              </div>
            </div>
            
            <div className="bg-card rounded-3xl p-8 shadow-xl border border-border/10 hover:shadow-2xl transition-all duration-300">
              <div className="flex items-center justify-between mb-6">
                <h4 className="text-xl font-bold text-foreground">Time Saved</h4>
                <Sparkles className="w-8 h-8 text-primary" />
              </div>
              <div className="text-5xl font-bold text-primary mb-2">15hrs</div>
              <div className="text-muted-foreground text-lg">Every week</div>
              <div className="mt-4 bg-purple-100 rounded-lg p-3">
                <p className="text-sm text-purple-700 font-medium">Now spends weekends with family</p>
              </div>
            </div>
          </div>
        </div>

        {/* Second Row - Testimonial and Customer Reach */}
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Left Column - Testimonial */}
          <div className="animate-fade-in" style={{ animationDelay: '0.4s' }}>
            <div className="bg-card/90 backdrop-blur-sm rounded-2xl p-8 border border-border/20 shadow-lg">
              <blockquote className="border-l-4 border-primary pl-6 italic text-xl text-foreground font-medium mb-6">
                "DataGent didn't just grow my business — it gave me my life back. 
                It's like having a business consultant, data analyst, and marketing expert working 24/7, 
                but they actually understand my bakery."
              </blockquote>
              
              <div className="flex items-center space-x-6">
                <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center text-white font-bold text-2xl shadow-lg">
                  J
                </div>
                <div>
                  <p className="font-bold text-foreground text-xl">Jane Wanjiku</p>
                  <p className="text-muted-foreground">Sweet Dreams Bakery, Nairobi</p>
                  <div className="flex items-center mt-2">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                    ))}
                    <span className="ml-2 text-sm text-muted-foreground">Verified customer</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Customer Reach Metric */}
          <div className="animate-fade-in" style={{ animationDelay: '0.6s' }}>
            <div className="bg-card rounded-3xl p-8 shadow-xl border border-border/10 hover:shadow-2xl transition-all duration-300">
              <div className="flex items-center justify-between mb-6">
                <h4 className="text-xl font-bold text-foreground">Customer Reach</h4>
                <Users className="w-8 h-8 text-blue-500" />
              </div>
              <div className="text-5xl font-bold text-blue-600 mb-2">3x</div>
              <div className="text-muted-foreground text-lg">More customers</div>
              <div className="mt-4 bg-blue-100 rounded-lg p-3">
                <p className="text-sm text-blue-700 font-medium">Automated social media brings new faces daily</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SuccessStorySection;
