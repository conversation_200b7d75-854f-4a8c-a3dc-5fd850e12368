
import { useState } from 'react';
import { toast } from "@/hooks/use-toast";
import type { WaitlistData } from '@/types';

export const useWaitlistSignup = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleWaitlistSignup = async (email: string): Promise<void> => {
    if (!email) {
      toast({
        title: "Email required",
        description: "Please enter a valid email address.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    
    try {
      // Simulate API call with realistic delay
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Store waitlist data (in a real app, this would be sent to a backend)
      const waitlistData: WaitlistData = {
        email,
        timestamp: new Date(),
      };
      
      // Store locally for demo purposes
      const existingData = localStorage.getItem('waitlist') || '[]';
      const waitlist = JSON.parse(existingData);
      waitlist.push(waitlistData);
      localStorage.setItem('waitlist', JSON.stringify(waitlist));
      
      toast({
        title: "Welcome to the waitlist! 🎉",
        description: "We'll notify you as soon as DataGent is available. Check your email for a confirmation.",
      });
    } catch (error) {
      console.error('Waitlist signup error:', error);
      toast({
        title: "Something went wrong",
        description: "Please try again in a moment.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    handleWaitlistSignup,
    isSubmitting,
  };
};
