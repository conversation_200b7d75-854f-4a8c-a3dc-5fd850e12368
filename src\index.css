
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 9%;

    --card: 0 0% 100%;
    --card-foreground: 0 0% 9%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 9%;

    --primary: 262 83% 58%;
    --primary-foreground: 0 0% 98%;

    --secondary: 220 14% 96%;
    --secondary-foreground: 0 0% 9%;

    --muted: 220 14% 96%;
    --muted-foreground: 215 16% 47%;

    --accent: 220 14% 96%;
    --accent-foreground: 0 0% 9%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 262 83% 58%;

    --radius: 0.75rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 0 0% 9%;
    --foreground: 0 0% 98%;

    --card: 0 0% 9%;
    --card-foreground: 0 0% 98%;

    --popover: 0 0% 9%;
    --popover-foreground: 0 0% 98%;

    --primary: 262 83% 58%;
    --primary-foreground: 0 0% 98%;

    --secondary: 217 32% 17%;
    --secondary-foreground: 0 0% 98%;

    --muted: 217 32% 17%;
    --muted-foreground: 215 20% 65%;

    --accent: 217 32% 17%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 63% 31%;
    --destructive-foreground: 0 0% 98%;

    --border: 217 32% 17%;
    --input: 217 32% 17%;
    --ring: 262 83% 58%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
  }

  html {
    scroll-behavior: smooth;
  }

  /* Improve focus visibility for accessibility */
  *:focus-visible {
    @apply outline-2 outline-offset-2 outline-ring;
  }
}

@layer utilities {
  .gradient-primary {
    background: linear-gradient(135deg, hsl(262 83% 58%) 0%, hsl(217 91% 60%) 100%);
  }
  
  .gradient-secondary {
    background: linear-gradient(135deg, hsl(217 91% 60%) 0%, hsl(262 83% 58%) 100%);
  }
  
  .text-gradient {
    background: linear-gradient(135deg, hsl(262 83% 58%) 0%, hsl(217 91% 60%) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .gradient-cta {
    background: linear-gradient(135deg, hsl(284 100% 65%) 0%, hsl(262 100% 75%) 50%, hsl(217 100% 70%) 100%);
    box-shadow: 0 8px 32px hsla(262, 83%, 58%, 0.4), 0 4px 16px hsla(217, 91%, 60%, 0.3);
  }
  
  .gradient-cta:hover {
    background: linear-gradient(135deg, hsl(284 100% 70%) 0%, hsl(262 100% 80%) 50%, hsl(217 100% 75%) 100%);
    box-shadow: 0 12px 48px hsla(262, 83%, 58%, 0.5), 0 6px 24px hsla(217, 91%, 60%, 0.4);
  }

  /* Optimized animations */
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-fade-in {
    animation: fadeIn 0.5s ease-out forwards;
  }

  .animate-scale-up {
    animation: scaleUp 0.3s ease-out;
  }

  /* Performance optimized hover effects */
  .hover-lift {
    @apply transition-transform duration-300 ease-out;
  }

  .hover-lift:hover {
    @apply -translate-y-1 shadow-lg;
  }

  /* Reduced motion for accessibility */
  @media (prefers-reduced-motion: reduce) {
    .animate-float,
    .animate-fade-in,
    .animate-scale-up {
      animation: none;
    }
    
    .hover-lift {
      transition: none;
    }
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes scaleUp {
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}
