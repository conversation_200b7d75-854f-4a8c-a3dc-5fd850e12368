
export interface WaitlistData {
  email: string;
  timestamp: Date;
}

export interface SectionProps {
  className?: string;
}

export interface HeroSectionProps extends SectionProps {}

export interface FinalCTASectionProps extends SectionProps {}

export interface NavigationItem {
  label: string;
  href: string;
}

export interface TestimonialData {
  id: string;
  name: string;
  company: string;
  location: string;
  content: string;
  rating: number;
  avatar: string;
}

export interface UseCaseData {
  id: string;
  title: string;
  description: string;
  icon: string;
  image: string;
}

export interface SuccessMetric {
  label: string;
  value: string;
  description: string;
  icon: React.ComponentType;
  color: string;
}
